import numpy as np
import pandas as pd


def count_data_in_rul_file(filename):
    """
    统计RUL文件中的数据数量

    Args:
        filename (str): RUL文件路径

    Returns:
        int: 数据行数
    """
    try:
        with open(filename, 'r') as file:
            lines = file.readlines()
            # 过滤掉空行
            data_lines = [line.strip() for line in lines if line.strip()]
            return len(data_lines)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return 0
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return 0


def analyze_rul_data(filename):
    """
    分析RUL数据的基本统计信息

    Args:
        filename (str): RUL文件路径
    """
    try:
        with open(filename, 'r') as file:
            lines = file.readlines()
            # 过滤掉空行并转换为数值
            data = []
            for line in lines:
                line = line.strip()
                if line:
                    try:
                        data.append(int(line))
                    except ValueError:
                        print(f"警告: 无法解析数据行: {line}")

            if data:
                print(f"文件: {filename}")
                print(f"数据总数: {len(data)}")
                print(f"最小值: {min(data)}")
                print(f"最大值: {max(data)}")
                print(f"平均值: {sum(data) / len(data):.2f}")
                print(f"前10个数据: {data[:10]}")
                return len(data)
            else:
                print("文件中没有有效数据")
                return 0

    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return 0
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return 0


if __name__ == "__main__":
    filename = "RUL_FD001.txt"

    # 统计数据数量
    count = count_data_in_rul_file(filename)
    print(f"RUL_FD001.txt 文件中共有 {count} 条数据")

    print("\n" + "="*50)

    # 详细分析
    analyze_rul_data(filename)


    with open('RUL_FD001.txt', 'r') as file:
        lines = file.readlines()
        print(len(file))
