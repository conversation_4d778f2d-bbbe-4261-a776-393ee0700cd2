### base config ###
full_field: &FULL_FIELD
  loss: 'l2'
  lr: 1E-3
  scheduler: 'ReduceLROnPlateau' # lichangyu todo
  num_data_workers: 4  # lichangyu todo
  dt: 1 # how many timesteps ahead the model will predict
  n_history: 0 #how many previous timesteps to consider
  prediction_type: 'iterative'
  prediction_length: 41 #applicable only if prediction_type == 'iterative'
  n_initial_conditions: 5 #applicable only if prediction_type == 'iterative'   # lichangyu todo???
  ics_type: "default"
  save_raw_forecasts: !!bool True
  save_channel: !!bool False
  masked_acc: !!bool False
  maskpath: None
  perturb: !!bool False
  add_grid: !!bool False
  N_grid_channels: 0
  gridtype: 'sinusoidal' #options 'sinusoidal' or 'linear'
  roll: !!bool False
  max_epochs: 50
  batch_size: 64

  #afno hyperparams
  num_blocks: 4
  nettype: 'afno'
  patch_size: 8
  width: 56  # lichangyu todo ???
  modes: 32   # lichangyu todo ???
  #options default, residual
  target: 'default'
  in_channels: [0,1]
  out_channels: [0,1] #must be same as in_channels if prediction_type == 'iterative'
  normalization: 'zscore' #options zscore (minmax not supported)
  train_data_path: '/pscratch/sd/j/jpathak/wind/train'
  valid_data_path: '/pscratch/sd/j/jpathak/wind/test'
  inf_data_path: '/pscratch/sd/j/jpathak/wind/out_of_sample' # test set path for inference
  exp_dir: '/pscratch/sd/j/jpathak/ERA5_expts_gtc/wind'
  time_means_path:   '/pscratch/sd/j/jpathak/wind/time_means.npy'
  global_means_path: '/pscratch/sd/j/jpathak/wind/global_means.npy'
  global_stds_path:  '/pscratch/sd/j/jpathak/wind/global_stds.npy'

  orography: !!bool False
  orography_path: None

  log_to_screen: !!bool True
  log_to_wandb: !!bool True
  save_checkpoint: !!bool True

  enable_nhwc: !!bool False
#  optimizer_type: 'FusedAdam'
  optimizer_type: ''
  crop_size_x: None
  crop_size_y: None

  two_step_training: !!bool False
  plot_animations: !!bool False

  add_noise: !!bool False
  noise_std: 0

afno_backbone: &backbone
  <<: *FULL_FIELD
  log_to_wandb: !!bool True
  lr: 5E-4
#  batch_size: 1
  max_epochs: 150
  scheduler: 'CosineAnnealingLR'
  in_channels: [0, 1 ,2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
  out_channels: [0, 1 ,2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
  orography: !!bool False
  orography_path: None
  exp_dir: '/home/<USER>/glh/mae'


  train_data_path: '/home/<USER>/codes/fcn/data/train'
  valid_data_path: '/home/<USER>/codes/fcn/data/test'
  inf_data_path:   '/home/<USER>/codes/fcn/data/out_of_sample'
  time_means_path:   '/home/<USER>/codes/fcn/additional/time_means.npy'
  global_means_path: '/home/<USER>/codes/fcn/additional/global_means.npy'
  global_stds_path:  '/home/<USER>/codes/fcn/additional/global_stds.npy'
  train_data_path_h5: '/home/<USER>/codes/fcn/data/train'
  valid_data_path_h5: '/home/<USER>/codes/fcn/data/test'
# lichangyu todo arguments for mae
  batch_size: 2
  epochs: 50
  accum_iter: 1
  model: 'mae_vit_base_patch16'
  input_size: [720, 1440]
  mask_ratio: 0.
  norm_pix_loss: !!bool False # lichangyu todo ??
  weight_decay: 0.05
  lr_new: None
  blr: 1e-3
  min_lr: 0.
  warmup_epochs: 40
  data_path: ''
  output_dir: ''
#  output_dir: '/home/<USER>/codes/mae/mae-main/output_dir'
  log_dir: './output_dir'
  device: 'cuda'
  seed: 0
  start_epoch: 0
  num_workers: 0
  pin_mem: !!bool True
  no_pin_mem: !!bool False
  world_size: 1
  local_rank: -1
  rank: 0
  dist_on_itp: !!bool False
  dist_url: 'env://'
  n_in_channels: 20
  n_out_channels: 20
  run_num: ''
  run_mode: 'pretrain'
  save_dir: ''
  yaml_config: './config/AFNO_v5_8_8.yaml'
  config: 'afno_backbone'
  patch_size: [8, 8]  # lichangyu todo aaa [16, 16]
  img_size: [720, 1440]
  iters: 0 #
  resuming: !!bool False
  checkpoint_path: ''
  pretrained_ckpt_path: ''

afno_backbone_orography: &backbone_orography
  <<: *backbone
  orography: !!bool True # lichangyu todo ??
  orography_path: '/home/<USER>/codes/fcn/data/static/orography.h5'

afno_backbone_finetune:
  <<: *backbone
  lr: 1E-4
  batch_size: 1
  log_to_wandb: !!bool True
  max_epochs: 2
  pretrained: !!bool True
  two_step_training: !!bool True
  pretrained_ckpt_path: '/pscratch/sd/s/shas1693/results/era5_wind/afno_backbone/0/training_checkpoints/best_ckpt.tar'

perturbations:
  <<: *backbone
  lr: 1E-4
  batch_size: 64
  max_epochs: 50
  pretrained: !!bool True
  two_step_training: !!bool True
  pretrained_ckpt_path: '/pscratch/sd/j/jpathak/ERA5_expts_gtc/wind/afno_20ch_bs_64_lr5em4_blk_8_patch_8_cosine_sched/1/training_checkpoints/best_ckpt.tar'
  prediction_length: 24
  ics_type: "datetime"
  n_perturbations: 100
  save_channel: !bool True
  save_idx: 4
  save_raw_forecasts: !!bool False
  date_strings: ["2018-01-01 00:00:00"]
  inference_file_tag: " "
  valid_data_path: "/pscratch/sd/j/jpathak/ "
  perturb: !!bool True
  n_level: 0.3

### PRECIP ###
precip: &precip
  <<: *backbone
  in_channels: [0, 1 ,2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
  out_channels: [0]
  nettype: 'afno'
  nettype_wind: 'afno'
  log_to_wandb: !!bool True
  lr: 2.5E-4
  batch_size: 1
  max_epochs: 25
  precip: '/home/<USER>/codes/FourCastNet/data/FCN_ERA5_data_v0/precip'
  time_means_path_tp: '/home/<USER>/codes/FourCastNet/additional/stats_v0/precip/time_means.npy'
#  precip: '/pscratch/sd/p/pharring/ERA5/precip/total_precipitation'
#  time_means_path_tp: '/pscratch/sd/p/pharring/ERA5/precip/total_precipitation/time_means.npy'
  model_wind_path: '/home/<USER>/codes/FourCastNet/NVLabs-FourCastNet/FourCastNet-master/results_ms_pre/era5_wind/afno_backbone_finetune/check_ft/training_checkpoints/best_ckpt.tar'
  precip_eps: !!float 1e-5

#result/afno_backbone_finetune/0/training_checkpoints/best_ckpt:
#  tar: